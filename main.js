// Electron 主程序入口文件
'use strict'
require('./global')
const { actualVersion, shortcutName, devWebPort } = require('./package.json')
const { app, protocol, BrowserWindow, shell, Menu, Tray } = require('electron')
const path = require('path')
const operationCenterServer = require('./server/index')
const { SYSTEM_PLATFORM_STR_DIC } = require('./hardware/enum')
const Logger = require('./hardware/log/index')
const log = new Logger()

let appTray

// 为协议注册特权---可访问摄像头
protocol.registerSchemesAsPrivileged([
	{
		scheme: 'http',
		privileges: {
			bypassCSP: true,
			secure: true,
			supportFetchAPI: true,
			corsEnabled: true,
			standard: true,
			allowServiceWorkers: true,
			stream: true
		}
	}
])

// 启动服务
function startServer() {
	operationCenterServer.init()
}

// 退出服务
function stopServer() {
	operationCenterServer.close()
}

// 创建应用主窗口
function createWindow() {
	if (!global.isCreateWindow) {
		return
	}
	global.mainWin = new BrowserWindow({
		title: shortcutName,
		icon: global.appIcon,
		width: global.serverConfig.winWidth || 1920,
		height: global.serverConfig.winHeight || 1080,
		useContentSize: true,
		frame: app.isPackaged,
		autoHideMenuBar: false,
		fullscreen: app.isPackaged,
		transparent: false,
		alwaysOnTop: global.serverConfig.alwaysOnTop == 1,
		webPreferences: {
			nodeIntegrationInSubFrames: true,
			nodeIntegration: true,
			contextIsolation: true,
			webSecurity: false,
			preload: path.join(global.extraResources, 'preload.js')
		}
	})
	global.mainWin.loadURL(`http://localhost:${app.isPackaged ? global.serverConfig.backHttpServerPort : devWebPort}/client.html`)
	global.mainWin.show()
	if (app.isPackaged) {
		global.mainWin.setFullScreen(false)
		global.mainWin.setFullScreen(true)
	}
	global.mainWin.on('closed', () => {
		global.mainWin = null
		app.quit(0)
	})
	global.mainWin.on('close', () => {
		app.quit()
	})
}

// 系统托盘加图标、右键菜单
function setSystemTray() {
	// 系统托盘右键菜单
	const trayMenuTemplate = [
		{
			label: `版本号：${actualVersion}`,
			click: () => {
				log.info(`actualVersion:${actualVersion}`)
			}
		},
		{
			label: '配置',
			click: () => {
				if (process.platform == SYSTEM_PLATFORM_STR_DIC.LINUX) {
					shell.openPath(global.serverConfigPath)
				} else {
					shell.openExternal(global.serverConfigPath)
				}
			}
		},
		{
			label: '退出',
			click: () => {
				app.exit(0)
				// app.quit()
			}
		}
	]

	// 系统托盘图标目录
	appTray = new Tray(global.appIcon)
	// 图标的上下文菜单
	const contextMenu = Menu.buildFromTemplate(trayMenuTemplate)
	// 设置此托盘图标的悬停提示内容
	appTray.setToolTip(shortcutName)
	// 设置此图标的上下文菜单
	appTray.setContextMenu(contextMenu)
}

// 实例检测
const gotTheLock = app.requestSingleInstanceLock()
if (!gotTheLock) {
	app.quit()
} else {
	app.on('window-all-closed', () => {
		if (process.platform !== 'darwin') {
			app.quit()
		} else {
			// 关闭服务
			stopServer()
			// 关闭日志扫描
			log.clearTimer()
		}
	})
	app.whenReady().then(() => {
		// 创建托盘
		setSystemTray()
		// 创建窗口
		createWindow()
		// 启动服务
		startServer()
		app.on('activate', () => {
			if (BrowserWindow.getAllWindows().length === 0) createWindow()
		})
		app.on('second-instance', (event, commandLine, workingDirectory) => {
			if (global.mainWin) {
				if (global.mainWin.isMinimized()) global.mainWin.restore()
				global.mainWin.focus()
			}
		})
	})
	app.on('quit', () => {
		// 关闭服务
		stopServer()
		// 关闭日志扫描
		log.clearTimer()
	})
}

// 禁用当前应用程序的硬件加速
app.disableHardwareAcceleration()
app.commandLine.appendSwitch('--disable-http-cache')
app.commandLine.appendSwitch('ignore-certificate-errors', 'true')
